<!DOCTYPE html>
<html>
<head>
    <title>User Type Test</title>
</head>
<body>
    <h1>User Information</h1>
    
    @if(Auth::check())
        <p><strong>User ID:</strong> {{ Auth::user()->id }}</p>
        <p><strong>User Name:</strong> {{ Auth::user()->name }}</p>
        <p><strong>User Email:</strong> {{ Auth::user()->email }}</p>
        <p><strong>User Type:</strong> {{ Auth::user()->type }}</p>
        
        @if(Auth::user()->type == 'super admin')
            <div style="color: green;">
                <h2>✅ You are a Super Admin!</h2>
                <p>You should be able to access the Login Page Customization section.</p>
            </div>
        @else
            <div style="color: red;">
                <h2>❌ You are NOT a Super Admin</h2>
                <p>Your user type is: <strong>{{ Auth::user()->type }}</strong></p>
                <p>You need to be a Super Admin to access the Login Page Customization section.</p>
            </div>
        @endif
        
        <hr>
        <h3>Route Test</h3>
        <form action="{{ route('login.customization.store') }}" method="POST" enctype="multipart/form-data">
            @csrf
            <p>If you can see this form without errors, the route is working correctly.</p>
            <button type="button" onclick="alert('Route is working!')">Test Route</button>
        </form>
        
    @else
        <p style="color: red;">You are not logged in!</p>
    @endif
</body>
</html>
