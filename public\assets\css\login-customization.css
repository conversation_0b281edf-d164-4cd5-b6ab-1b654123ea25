/* Login Page Customization Styles */

/* Settings Page Styling */
#login-customization .card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

#login-customization .card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1.25rem;
}

#login-customization .card-header h5 {
    margin-bottom: 0.5rem;
    color: #5a5c69;
    font-weight: 600;
}

#login-customization .card-header small {
    color: #858796;
}

/* File Upload Styling */
.choose-files label {
    cursor: pointer;
    display: block;
}

.choose-files .company_logo_update {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px dashed transparent;
}

.choose-files .company_logo_update:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.choose-files input[type="file"] {
    display: none;
}

/* Image Preview Styling */
.logo-content {
    border-radius: 8px;
    overflow: hidden;
}

.logo-content img {
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.logo-content img:hover {
    transform: scale(1.05);
}

.logo-content .text-center {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.logo-content .text-center:hover {
    border-color: #007bff;
    background: #f0f8ff;
}

/* Color Picker Styling */
input[type="color"] {
    width: 100%;
    height: 45px;
    border: 2px solid #e3e6f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

input[type="color"]:hover {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Section Headers */
#login-customization h6 {
    color: #5a5c69;
    font-weight: 600;
    font-size: 1.1rem;
}

#login-customization hr {
    border-top: 2px solid #e3e6f0;
    margin: 1rem 0;
}

/* Form Controls */
#login-customization .form-control,
#login-customization .select2 {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

#login-customization .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button Styling */
#login-customization .btn-submit {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

#login-customization .btn-submit:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* Icons */
#login-customization .ti {
    margin-right: 8px;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    #login-customization .card-body {
        padding: 1rem;
    }
    
    #login-customization .col-lg-4,
    #login-customization .col-lg-6 {
        margin-bottom: 1.5rem;
    }
}

/* Animation for file upload feedback */
@keyframes uploadSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.choose-files.success .company_logo_update {
    animation: uploadSuccess 0.6s ease;
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

/* Preview Image Hover Effects */
.logo-content a {
    display: block;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.logo-content a::before {
    content: '🔍';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
    font-size: 1.2rem;
}

.logo-content a:hover::before {
    opacity: 1;
}

/* Small text styling */
#login-customization small.text-muted {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
    display: block;
}

/* Card footer */
#login-customization .card-footer {
    background-color: #f8f9fc;
    border-top: 1px solid #e3e6f0;
    padding: 1.25rem;
}

/* Success/Error Messages */
.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

/* Login Page Background Override Styles */
.custom-login-body {
    background: var(--login-primary-color, #007bff) !important;
}

.custom-login {
    background: var(--login-primary-color, #007bff) !important;
    min-height: 100vh;
}

.custom-login .bg-login,
.custom-login .bg-primary {
    background: var(--login-primary-color, #007bff) !important;
}

/* Ensure full coverage */
html, body {
    height: 100%;
}

body.custom-login-body {
    background: var(--login-primary-color, #007bff) !important;
    background-attachment: fixed !important;
}

/* Override any conflicting styles */
.custom-login * {
    box-sizing: border-box;
}

.custom-login .custom-login-inner {
    background: transparent !important;
}

.custom-login .card {
    background: var(--login-background-color, #ffffff) !important;
}
