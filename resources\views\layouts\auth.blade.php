<!DOCTYPE html>
@php
    use App\Models\Utility;

    $setting = Utility::settings();
    $company_logo = $setting['company_logo_dark'] ?? '';
    $company_logos = $setting['company_logo_light'] ?? '';
    $company_favicon = $setting['company_favicon'] ?? '';

    $logo = \App\Models\Utility::get_file('uploads/logo/');

    $color = !empty($setting['color']) ? $setting['color'] : 'theme-3';

    if(isset($setting['color_flag']) && $setting['color_flag'] == 'true')
    {
        $themeColor = 'custom-color';
    }
    else {
        $themeColor = $color;
    }

    $company_logo = \App\Models\Utility::GetLogo();
    $SITE_RTL = isset($setting['SITE_RTL']) ? $setting['SITE_RTL'] : 'off';

    $lang = \App::getLocale('lang');
    if ($lang == 'ar' || $lang == 'he') {
        $SITE_RTL = 'on';
    }
    elseif($SITE_RTL == 'on')
    {
        $SITE_RTL = 'on';
    }
    else {
        $SITE_RTL = 'off';
    }

    $metatitle = isset($setting['meta_title']) ? $setting['meta_title'] : '';
    $metsdesc = isset($setting['meta_desc']) ? $setting['meta_desc'] : '';
    $meta_image = \App\Models\Utility::get_file('uploads/meta/');
    $meta_logo = isset($setting['meta_image']) ? $setting['meta_image'] : '';
    $get_cookie = isset($setting['enable_cookie']) ? $setting['enable_cookie'] : '';

    // Login Page Customization Settings
    $login_customization_enabled = isset($setting['enable_login_customization']) && $setting['enable_login_customization'] == 'on';
    $login_primary_color = isset($setting['login_primary_color']) ? $setting['login_primary_color'] : '#007bff';
    $login_background_color = isset($setting['login_background_color']) ? $setting['login_background_color'] : '#ffffff';
    $login_bg_animation = isset($setting['login_bg_animation']) ? $setting['login_bg_animation'] : 'off';

    // Custom login assets
    $login_custom_logo = '';
    $login_favicon = '';
    $login_bg_images = [];

    if ($login_customization_enabled) {
        $login_assets_path = asset('storage/login_customization/');

        // Check for custom logo
        if (file_exists(public_path('storage/login_customization/login_custom_logo.png'))) {
            $login_custom_logo = $login_assets_path . 'login_custom_logo.png';
        }

        // Check for custom favicon
        if (file_exists(public_path('storage/login_customization/login_favicon.png'))) {
            $login_favicon = $login_assets_path . 'login_favicon.png';
        }

        // Check for background images
        for ($i = 1; $i <= 3; $i++) {
            if (file_exists(public_path('storage/login_customization/login_bg_image_' . $i . '.png'))) {
                $login_bg_images[] = $login_assets_path . 'login_bg_image_' . $i . '.png';
            }
        }
    }

@endphp

{{-- <html lang="en" dir="{{$SITE_RTL == 'on' ? 'rtl' : '' }}"> --}}
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"
    dir="{{ $SITE_RTL == 'on' ? 'rtl' : '' }}">

<head>
    <title>
        {{ Utility::getValByName('title_text') ? Utility::getValByName('title_text') : config('app.name', 'ERPGO') }}
        - @yield('page-title')</title>

    <meta name="title" content="{{ $metatitle }}">
    <meta name="description" content="{{ $metsdesc }}">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ env('APP_URL') }}">
    <meta property="og:title" content="{{ $metatitle }}">
    <meta property="og:description" content="{{ $metsdesc }}">
    <meta property="og:image" content="{{ $meta_image . $meta_logo }}">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ env('APP_URL') }}">
    <meta property="twitter:title" content="{{ $metatitle }}">
    <meta property="twitter:description" content="{{ $metsdesc }}">
    <meta property="twitter:image" content="{{ $meta_image . $meta_logo }}">


    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="Dashboard Template Description" />
    <meta name="keywords" content="Dashboard Template" />
    <meta name="author" content="WorkDo" />

    <!-- Favicon icon -->
    <link rel="icon"
        href="{{ $login_customization_enabled && !empty($login_favicon) ? $login_favicon : $logo . '/' . (isset($company_favicon) && !empty($company_favicon) ? $company_favicon : 'favicon.png') }}"
        type="image/x-icon" />

    <!-- font css -->
    <link rel="stylesheet" href="{{ asset('assets/fonts/tabler-icons.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/feather.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/fontawesome.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/material.css') }}">

    <!-- vendor css -->

    @if ($SITE_RTL == 'on')
        <link rel="stylesheet" href="{{ asset('assets/css/style-rtl.css') }}" id="main-style-link">
    @endif

    @if ($setting['cust_darklayout'] == 'on')
        <link rel="stylesheet" href="{{ asset('assets/css/style-dark.css') }}">
    @endif

    @if ($SITE_RTL != 'on' && $setting['cust_darklayout'] != 'on')
        <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}" id="main-style-link">
    @endif


    @if ($SITE_RTL == 'on')
        <link rel="stylesheet" href="{{ asset('assets/css/custom-auth-rtl.css') }}" id="main-style-link">
        @else
        <link rel="stylesheet" href="{{ asset('assets/css/custom-auth.css') }}" id="main-style-link">
    @endif

    @if ($setting['cust_darklayout'] == 'on')
        <link rel="stylesheet" href="{{ asset('assets/css/custom-auth-dark.css') }}" id="main-style-link">
    @endif

    <style>
        :root {
            --color-customColor: <?= $color ?>;
            @if($login_customization_enabled)
            --login-primary-color: {{ $login_primary_color }};
            --login-background-color: {{ $login_background_color }};
            @endif
        }

        @if($login_customization_enabled)
        /* Login Page Customization Styles */
        .custom-login .bg-login {
            background: {{ $login_primary_color }} !important;
        }

        .custom-login .bg-primary {
            background: {{ $login_primary_color }} !important;
        }

        .custom-login .card {
            background-color: {{ $login_background_color }} !important;
        }

        .custom-login .btn-primary {
            background-color: {{ $login_primary_color }} !important;
            border-color: {{ $login_primary_color }} !important;
        }

        .custom-login .btn-primary:hover {
            background-color: {{ $login_primary_color }}dd !important;
            border-color: {{ $login_primary_color }}dd !important;
        }

        .custom-login .form-control:focus {
            border-color: {{ $login_primary_color }} !important;
            box-shadow: 0 0 0 0.2rem {{ $login_primary_color }}25 !important;
        }

        .custom-login a {
            color: {{ $login_primary_color }} !important;
        }

        /* Override the entire background */
        .custom-login {
            background: {{ $login_primary_color }} !important;
        }

        /* Make sure the login area has the custom background color */
        .custom-login .custom-login-inner {
            background: {{ $login_background_color }} !important;
        }

        /* Override body background when customization is enabled */
        body.custom-login-body {
            background: {{ $login_primary_color }} !important;
        }

        body.custom-login {
            background: {{ $login_primary_color }} !important;
        }

        /* Ensure the main wrapper uses custom colors */
        .custom-login .custom-wrapper {
            background: transparent !important;
        }

        /* Header styling */
        .custom-login header.dash-header {
            background: transparent !important;
        }

        .custom-login .navbar {
            background: transparent !important;
        }

        /* Force background color on all elements */
        .custom-login,
        .custom-login .bg-login,
        .custom-login .bg-primary,
        .custom-login::before,
        .custom-login::after {
            background: {{ $login_primary_color }} !important;
        }

        /* Specific targeting for the problematic div */
        .custom-login div.bg-login.bg-primary {
            background: {{ $login_primary_color }} !important;
            background-color: {{ $login_primary_color }} !important;
        }

        /* Additional overrides for stubborn elements */
        html {
            background: {{ $login_primary_color }} !important;
        }

        /* Override Bootstrap primary color */
        .bg-primary {
            background-color: {{ $login_primary_color }} !important;
        }

        /* Target all possible background elements */
        .custom-login *[class*="bg-"] {
            background: {{ $login_primary_color }} !important;
        }

        @if(!empty($login_bg_images))
        .custom-login {
            position: relative;
            overflow: hidden;
        }

        .custom-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('{{ $login_bg_images[0] }}');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0.1;
            z-index: -1;
            @if($login_bg_animation == 'slideshow' && count($login_bg_images) > 1)
            animation: loginBgSlideshow {{ count($login_bg_images) * 5 }}s infinite;
            @elseif($login_bg_animation == 'fade' && count($login_bg_images) > 1)
            animation: loginBgFade {{ count($login_bg_images) * 3 }}s infinite;
            @endif
        }

        @if($login_bg_animation == 'slideshow' && count($login_bg_images) > 1)
        @keyframes loginBgSlideshow {
            @for($i = 0; $i < count($login_bg_images); $i++)
            {{ ($i * 100 / count($login_bg_images)) }}% {
                background-image: url('{{ $login_bg_images[$i] }}');
            }
            @endfor
        }
        @endif

        @if($login_bg_animation == 'fade' && count($login_bg_images) > 1)
        @keyframes loginBgFade {
            @for($i = 0; $i < count($login_bg_images); $i++)
            {{ ($i * 100 / count($login_bg_images)) }}%, {{ (($i + 1) * 100 / count($login_bg_images) - 10) }}% {
                background-image: url('{{ $login_bg_images[$i] }}');
                opacity: 0.1;
            }
            {{ (($i + 1) * 100 / count($login_bg_images) - 5) }}% {
                background-image: url('{{ $login_bg_images[$i] }}');
                opacity: 0.05;
            }
            @endfor
        }
        @endif
        @endif
        @endif
    </style>

    <link rel="stylesheet" href="{{ asset('css/custom-color.css') }}">

    <link rel="stylesheet" href="{{ asset('assets/css/customizer.css') }}">

    <link rel="stylesheet" href="{{ asset('css/custom.css') }}">

    <!-- Microsoft Clarity Tracking Code -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "rji5u8kka7");
    </script>
</head>

<body class="{{ $themeColor }} @if($login_customization_enabled) custom-login-body @endif" @if($login_customization_enabled) style="background: {{ $login_primary_color }} !important;" @endif>
    <div class="custom-login" @if($login_customization_enabled) style="background: {{ $login_primary_color }} !important;" @endif>
        <div class="login-bg-img" style="display: none;">
            <img src="{{ isset($setting['color_flag']) && $setting['color_flag'] == 'false' ? asset('assets/images/auth/'.$color.'.svg') : asset('assets/images/auth/theme-3.svg') }}" class="login-bg-1">
            <img src="{{ asset('assets/images/auth/common.svg') }}" class="login-bg-2">
        </div>
        <div class="bg-login bg-primary" @if($login_customization_enabled) style="background: {{ $login_primary_color }} !important; background-color: {{ $login_primary_color }} !important;" @endif></div>
        <div class="custom-login-inner">
            <header class="dash-header">
                <nav class="navbar navbar-expand-md default">
                    <div class="container">
                        <div class="navbar-brand">

                        <a class="navbar-brand" href="#">
                            @if ($login_customization_enabled && !empty($login_custom_logo))
                                <img class="logo" src="{{ $login_custom_logo }}" alt="" loading="lazy"/>
                            @elseif ($setting['cust_darklayout'] == 'on')
                                <img class="logo"
                                    src="{{ $logo . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-light.png') . '?' . time() }}"
                                    alt="" loading="lazy"/>
                            @else
                                <img class="logo"
                                    src="{{ $logo . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png') . '?' . time() }}"
                                    alt="" loading="lazy"/>
                            @endif
                        </a>


                        </div>
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                            data-bs-target="#navbarlogin">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarlogin">
                            <ul class="navbar-nav align-items-center ms-auto mb-2 mb-lg-0">
                                @include('landingpage::layouts.buttons')
                                @yield('language-bar')
                            </ul>
                        </div>
                    </div>
                </nav>
            </header>
            <main class="custom-wrapper">
                <div class="custom-row">
                    <div class="card">
                        @yield('content')
                    </div>
                </div>
            </main>
            <footer>
                <div class="auth-footer">
                    <div class="container">
                        <div class="row">
                            <div class="col-12">
                                <span>&copy; {{ date('Y') }}
                                    {{ App\Models\Utility::getValByName('footer_text') ? App\Models\Utility::getValByName('footer_text') : config('app.name', 'Storego Saas') }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    @if ($get_cookie == 'on')
        @include('layouts.cookie_consent')
    @endif

    <!-- [ auth-signup ] end -->

    <!-- Required Js -->
    <script src="{{ asset('assets/js/vendor-all.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/bootstrap.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/feather.min.js') }}"></script>
    <script src="{{ asset('js/jquery.min.js') }}"></script>
    <script src="{{ asset('js/custom.js') }}"></script>


    <script>
        feather.replace();
    </script>

    @if (\App\Models\Utility::getValByName('cust_darklayout') == 'on')
        <style>
            .g-recaptcha {
                filter: invert(1) hue-rotate(180deg) !important;
            }
        </style>
    @endif


    <script>
        feather.replace();
        var pctoggle = document.querySelector("#pct-toggler");
        if (pctoggle) {
            pctoggle.addEventListener("click", function() {
                if (
                    !document.querySelector(".pct-customizer").classList.contains("active")
                ) {
                    document.querySelector(".pct-customizer").classList.add("active");
                } else {
                    document.querySelector(".pct-customizer").classList.remove("active");
                }
            });
        }

        var themescolors = document.querySelectorAll(".themes-color > a");
        for (var h = 0; h < themescolors.length; h++) {
            var c = themescolors[h];

            c.addEventListener("click", function(event) {
                var targetElement = event.target;
                if (targetElement.tagName == "SPAN") {
                    targetElement = targetElement.parentNode;
                }
                var temp = targetElement.getAttribute("data-value");
                removeClassByPrefix(document.querySelector("body"), "theme-");
                document.querySelector("body").classList.add(temp);
            });
        }
        function removeClassByPrefix(node, prefix) {
            for (let i = 0; i < node.classList.length; i++) {
                let value = node.classList[i];
                if (value.startsWith(prefix)) {
                    node.classList.remove(value);
                }
            }
        }
    </script>
    @stack('custom-scripts')

</body>

</html>
